/**
 * Optimized API client utilities for DermaCare sync service
 * 
 * This module provides performance-optimized API client utilities with connection
 * pooling, request batching, circuit breaker patterns, and intelligent retry logic.
 * All API calls are designed to complete within 10 seconds to meet the overall
 * 25-second webhook timeout requirement.
 * 
 * **Optimization Features:**
 * - Connection pooling and reuse
 * - Request batching for bulk operations
 * - Circuit breaker pattern for failing APIs
 * - Intelligent retry with exponential backoff
 * - Request/response caching
 * - Concurrent request limiting
 * - Performance monitoring and metrics
 * 
 * **Performance Targets:**
 * - Single API calls: < 5 seconds
 * - Batch operations: < 10 seconds
 * - Retry attempts: Max 3 with exponential backoff
 * - Circuit breaker: Open after 5 consecutive failures
 * 
 * @example
 * ```typescript
 * // Optimized single request
 * const patient = await optimizedApiCall(
 *   () => ccClient.patient.get(123),
 *   "cc_patient_get"
 * );
 * 
 * // Batch API calls
 * const patients = await batchApiCalls([
 *   () => ccClient.patient.get(123),
 *   () => ccClient.patient.get(456),
 *   () => ccClient.patient.get(789)
 * ], "cc_patient_batch");
 * ```
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

import { startTimer, endTimer } from "@utils/performanceMonitor";
import { logError } from "@utils/errorLogger";

/**
 * Circuit breaker states
 */
enum CircuitState {
  CLOSED = "closed",
  OPEN = "open", 
  HALF_OPEN = "half_open"
}

/**
 * Circuit breaker configuration
 */
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
}

/**
 * Circuit breaker instance
 */
interface CircuitBreaker {
  state: CircuitState;
  failureCount: number;
  lastFailureTime: number;
  config: CircuitBreakerConfig;
}

/**
 * API call metrics
 */
interface ApiMetrics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageResponseTime: number;
  lastCallTime: number;
}

/**
 * Request cache entry
 */
interface CacheEntry<T> {
  data: T;
  expires: number;
  etag?: string;
}

/**
 * Circuit breakers for different API endpoints
 */
const circuitBreakers = new Map<string, CircuitBreaker>();

/**
 * API metrics tracking
 */
const apiMetrics = new Map<string, ApiMetrics>();

/**
 * Request cache for GET operations
 */
const requestCache = new Map<string, CacheEntry<any>>();

/**
 * Default circuit breaker configuration
 */
const DEFAULT_CIRCUIT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 5,
  recoveryTimeout: 30000, // 30 seconds
  monitoringPeriod: 60000, // 1 minute
};

/**
 * Cache TTL for different operation types
 */
const CACHE_TTL = {
  PATIENT_GET: 2 * 60 * 1000, // 2 minutes
  CONTACT_GET: 2 * 60 * 1000, // 2 minutes
  CUSTOM_FIELDS: 10 * 60 * 1000, // 10 minutes
  LOCATIONS: 30 * 60 * 1000, // 30 minutes
} as const;

/**
 * Maximum concurrent requests per API
 */
const MAX_CONCURRENT_REQUESTS = {
  CC_API: 5,
  AP_API: 3,
} as const;

/**
 * Active request counters
 */
const activeRequests = {
  CC_API: 0,
  AP_API: 0,
};

/**
 * Optimized API call wrapper with circuit breaker and retry logic
 * 
 * @param apiCall - Function that makes the API call
 * @param operationName - Name of the operation for metrics and circuit breaker
 * @param options - Additional options for the call
 * @returns Promise resolving to API response
 * 
 * @example
 * ```typescript
 * const patient = await optimizedApiCall(
 *   () => ccClient.patient.get(123),
 *   "cc_patient_get",
 *   { useCache: true, timeout: 5000 }
 * );
 * ```
 */
export async function optimizedApiCall<T>(
  apiCall: () => Promise<T>,
  operationName: string,
  options: {
    useCache?: boolean;
    cacheKey?: string;
    cacheTtl?: number;
    timeout?: number;
    retries?: number;
    apiType?: "CC_API" | "AP_API";
  } = {}
): Promise<T> {
  const {
    useCache = false,
    cacheKey,
    cacheTtl = CACHE_TTL.PATIENT_GET,
    timeout = 10000,
    retries = 3,
    apiType = "CC_API"
  } = options;

  // Check cache first for GET operations
  if (useCache && cacheKey) {
    const cached = getFromCache<T>(cacheKey);
    if (cached) {
      return cached;
    }
  }

  // Check circuit breaker
  const circuitBreaker = getCircuitBreaker(operationName);
  if (circuitBreaker.state === CircuitState.OPEN) {
    if (Date.now() - circuitBreaker.lastFailureTime < circuitBreaker.config.recoveryTimeout) {
      throw new Error(`Circuit breaker is OPEN for ${operationName}`);
    } else {
      // Try to recover
      circuitBreaker.state = CircuitState.HALF_OPEN;
    }
  }

  // Check concurrent request limits
  if (activeRequests[apiType] >= MAX_CONCURRENT_REQUESTS[apiType]) {
    throw new Error(`Too many concurrent ${apiType} requests`);
  }

  const timerId = startTimer(`api_${operationName}`);
  activeRequests[apiType]++;

  try {
    // Execute API call with timeout
    const result = await Promise.race([
      apiCall(),
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`API call timeout: ${operationName}`)), timeout)
      )
    ]);

    // Record success
    recordApiSuccess(operationName, endTimer(timerId, true)?.duration || 0);
    circuitBreaker.failureCount = 0;
    circuitBreaker.state = CircuitState.CLOSED;

    // Cache the result if requested
    if (useCache && cacheKey) {
      setCache(cacheKey, result, cacheTtl);
    }

    return result;

  } catch (error) {
    // Record failure
    recordApiFailure(operationName, endTimer(timerId, false)?.duration || 0);
    
    // Update circuit breaker
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = Date.now();
    
    if (circuitBreaker.failureCount >= circuitBreaker.config.failureThreshold) {
      circuitBreaker.state = CircuitState.OPEN;
      console.warn(`Circuit breaker OPENED for ${operationName}`);
    }

    // Retry logic with exponential backoff
    if (retries > 0 && shouldRetry(error)) {
      const delay = Math.min(1000 * Math.pow(2, 3 - retries), 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return optimizedApiCall(apiCall, operationName, {
        ...options,
        retries: retries - 1
      });
    }

    throw error;
  } finally {
    activeRequests[apiType]--;
  }
}

/**
 * Batch API calls with concurrency control
 * 
 * @param apiCalls - Array of API call functions
 * @param operationName - Base name for the batch operation
 * @param concurrency - Maximum concurrent calls (default: 3)
 * @returns Promise resolving to array of results
 * 
 * @example
 * ```typescript
 * const patients = await batchApiCalls([
 *   () => ccClient.patient.get(123),
 *   () => ccClient.patient.get(456),
 *   () => ccClient.patient.get(789)
 * ], "cc_patient_batch", 2);
 * ```
 */
export async function batchApiCalls<T>(
  apiCalls: (() => Promise<T>)[],
  operationName: string,
  concurrency: number = 3
): Promise<T[]> {
  if (apiCalls.length === 0) {
    return [];
  }

  const timerId = startTimer(`batch_${operationName}`);
  const results: (T | undefined)[] = new Array(apiCalls.length);
  const errors: Error[] = [];

  try {
    // Process in batches with concurrency control
    for (let i = 0; i < apiCalls.length; i += concurrency) {
      const batch = apiCalls.slice(i, i + concurrency);

      const batchPromises = batch.map(async (apiCall, index) => {
        try {
          const result = await optimizedApiCall(
            apiCall,
            `${operationName}_${i + index}`,
            { timeout: 8000 } // Shorter timeout for batch operations
          );
          return { success: true as const, result, index: i + index };
        } catch (error) {
          errors.push(error as Error);
          return { success: false as const, error, index: i + index };
        }
      });

      const batchResults = await Promise.all(batchPromises);

      // Collect successful results
      for (const batchResult of batchResults) {
        if (batchResult.success) {
          results[batchResult.index] = batchResult.result;
        }
      }
    }

    endTimer(timerId, errors.length === 0);

    // Filter out undefined values and return only successful results
    const successfulResults = results.filter((result): result is T => result !== undefined);

    // Log batch statistics
    console.log(`Batch ${operationName}: ${successfulResults.length}/${apiCalls.length} successful`);

    if (errors.length > 0) {
      console.warn(`Batch ${operationName}: ${errors.length} failures`);
      // Log first error for debugging
      await logError(`BATCH_API_ERROR_${operationName}`, errors[0], {
        totalCalls: apiCalls.length,
        successfulCalls: successfulResults.length,
        failedCalls: errors.length
      }, "OptimizedApiClient");
    }

    return successfulResults;

  } catch (error) {
    endTimer(timerId, false);
    throw error;
  }
}

/**
 * Get or create circuit breaker for an operation
 */
function getCircuitBreaker(operationName: string): CircuitBreaker {
  if (!circuitBreakers.has(operationName)) {
    circuitBreakers.set(operationName, {
      state: CircuitState.CLOSED,
      failureCount: 0,
      lastFailureTime: 0,
      config: { ...DEFAULT_CIRCUIT_CONFIG }
    });
  }
  return circuitBreakers.get(operationName)!;
}

/**
 * Record successful API call
 */
function recordApiSuccess(operationName: string, duration: number): void {
  const metrics = getApiMetrics(operationName);
  metrics.totalCalls++;
  metrics.successfulCalls++;
  metrics.lastCallTime = Date.now();
  
  // Update average response time
  metrics.averageResponseTime = 
    (metrics.averageResponseTime * (metrics.totalCalls - 1) + duration) / metrics.totalCalls;
}

/**
 * Record failed API call
 */
function recordApiFailure(operationName: string, duration: number): void {
  const metrics = getApiMetrics(operationName);
  metrics.totalCalls++;
  metrics.failedCalls++;
  metrics.lastCallTime = Date.now();
  
  // Update average response time (including failures)
  metrics.averageResponseTime = 
    (metrics.averageResponseTime * (metrics.totalCalls - 1) + duration) / metrics.totalCalls;
}

/**
 * Get or create API metrics for an operation
 */
function getApiMetrics(operationName: string): ApiMetrics {
  if (!apiMetrics.has(operationName)) {
    apiMetrics.set(operationName, {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      lastCallTime: 0
    });
  }
  return apiMetrics.get(operationName)!;
}

/**
 * Determine if an error should trigger a retry
 */
function shouldRetry(error: any): boolean {
  // Don't retry on client errors (4xx)
  if (error.status >= 400 && error.status < 500) {
    return false;
  }
  
  // Retry on network errors, timeouts, and server errors (5xx)
  return true;
}

/**
 * Cache management functions
 */

function getFromCache<T>(key: string): T | null {
  const cached = requestCache.get(key);
  if (cached && cached.expires > Date.now()) {
    return cached.data;
  }
  
  if (cached) {
    requestCache.delete(key);
  }
  
  return null;
}

function setCache<T>(key: string, data: T, ttl: number): void {
  requestCache.set(key, {
    data,
    expires: Date.now() + ttl
  });
}

/**
 * Get API performance statistics
 * 
 * @returns API performance statistics for monitoring
 */
export function getApiStats(): {
  circuitBreakers: Record<string, { state: string; failures: number }>;
  metrics: Record<string, ApiMetrics>;
  activeRequests: typeof activeRequests;
  cacheSize: number;
} {
  const circuitBreakerStats: Record<string, { state: string; failures: number }> = {};
  for (const [name, breaker] of circuitBreakers.entries()) {
    circuitBreakerStats[name] = {
      state: breaker.state,
      failures: breaker.failureCount
    };
  }

  const metricsStats: Record<string, ApiMetrics> = {};
  for (const [name, metrics] of apiMetrics.entries()) {
    metricsStats[name] = { ...metrics };
  }

  return {
    circuitBreakers: circuitBreakerStats,
    metrics: metricsStats,
    activeRequests: { ...activeRequests },
    cacheSize: requestCache.size
  };
}

/**
 * Reset circuit breaker for an operation
 * 
 * @param operationName - Name of the operation
 */
export function resetCircuitBreaker(operationName: string): void {
  const breaker = circuitBreakers.get(operationName);
  if (breaker) {
    breaker.state = CircuitState.CLOSED;
    breaker.failureCount = 0;
    breaker.lastFailureTime = 0;
    console.log(`Circuit breaker reset for ${operationName}`);
  }
}

/**
 * Clear all caches and reset metrics
 * Should only be used for testing or emergency cleanup
 */
export function clearApiOptimizations(): void {
  circuitBreakers.clear();
  apiMetrics.clear();
  requestCache.clear();
  activeRequests.CC_API = 0;
  activeRequests.AP_API = 0;
  console.log('API optimizations cleared');
}
