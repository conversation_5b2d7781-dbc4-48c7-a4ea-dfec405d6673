/**
 * Webhook retry mechanism for DermaCare sync service
 * 
 * Provides intelligent retry logic for failed webhook processing with:
 * - Exponential backoff with jitter
 * - Maximum retry attempts (3)
 * - Timeout awareness (25-second limit)
 * - Comprehensive error logging
 * - Circuit breaker pattern integration
 * 
 * **Retry Strategy:**
 * - Attempt 1: Immediate retry after 1 second
 * - Attempt 2: Retry after 2 seconds
 * - Attempt 3: Retry after 4 seconds
 * - Total max time: ~7 seconds for retries
 * 
 * **Error Classification:**
 * - Retryable: Network errors, timeouts, 5xx server errors
 * - Non-retryable: 4xx client errors, validation errors
 * 
 * @example
 * ```typescript
 * const result = await withRetry(
 *   async () => await processWebhookEvent(context),
 *   {
 *     maxAttempts: 3,
 *     baseDelay: 1000,
 *     maxDelay: 5000,
 *     timeoutMs: 20000,
 *     operationName: "webhook_processing"
 *   }
 * );
 * ```
 * 
 * @since 2.0.0
 * @version 2.0.0
 */

import { endTimer, startTimer } from "./performanceMonitor";
import { logError } from "./errorLogger";

/**
 * Retry configuration options
 */
interface RetryConfig {
  /** Maximum number of retry attempts (default: 3) */
  maxAttempts: number;
  /** Base delay in milliseconds (default: 1000) */
  baseDelay: number;
  /** Maximum delay in milliseconds (default: 5000) */
  maxDelay: number;
  /** Total timeout in milliseconds (default: 20000) */
  timeoutMs: number;
  /** Operation name for logging (default: "operation") */
  operationName: string;
  /** Custom retry condition function */
  shouldRetry?: (error: unknown) => boolean;
}

/**
 * Retry attempt result
 */
interface RetryAttempt {
  attempt: number;
  success: boolean;
  error?: unknown;
  duration: number;
  delay?: number;
}

/**
 * Retry operation result
 */
interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: unknown;
  attempts: RetryAttempt[];
  totalDuration: number;
  finalAttempt: number;
}

/**
 * Default retry configuration optimized for webhook processing
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 5000,  // 5 seconds
  timeoutMs: 20000, // 20 seconds (leave 5 seconds buffer for other operations)
  operationName: "webhook_operation",
};

/**
 * Execute an operation with retry logic and exponential backoff
 * 
 * @param operation - Async operation to execute
 * @param config - Retry configuration options
 * @returns Promise resolving to retry result
 * 
 * @example
 * ```typescript
 * const result = await withRetry(
 *   async () => await processPatient(patientData),
 *   {
 *     maxAttempts: 3,
 *     baseDelay: 1000,
 *     operationName: "patient_processing"
 *   }
 * );
 * 
 * if (result.success) {
 *   console.log("Operation succeeded:", result.result);
 * } else {
 *   console.error("Operation failed after retries:", result.error);
 * }
 * ```
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<RetryResult<T>> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  const attempts: RetryAttempt[] = [];
  const startTime = Date.now();
  const timer = startTimer(`retry_${finalConfig.operationName}`);

  let lastError: unknown;
  let result: T | undefined;

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    const attemptTimer = startTimer(`retry_attempt_${attempt}`);
    
    try {
      // Check if we have enough time left for this attempt
      const elapsed = Date.now() - startTime;
      if (elapsed >= finalConfig.timeoutMs) {
        const timeoutError = new Error(`Retry timeout exceeded: ${elapsed}ms >= ${finalConfig.timeoutMs}ms`);
        attempts.push({
          attempt,
          success: false,
          error: timeoutError,
          duration: 0,
        });
        lastError = timeoutError;
        break;
      }

      // Execute the operation
      result = await operation();
      
      const attemptResult = endTimer(attemptTimer, true);
      attempts.push({
        attempt,
        success: true,
        duration: attemptResult?.duration ?? 0,
      });

      // Success - return immediately
      const totalDuration = endTimer(timer, true)?.duration ?? 0;
      return {
        success: true,
        result,
        attempts,
        totalDuration,
        finalAttempt: attempt,
      };

    } catch (error) {
      lastError = error;
      const attemptResult = endTimer(attemptTimer, false);
      
      // Check if this error should trigger a retry
      const shouldRetry = finalConfig.shouldRetry 
        ? finalConfig.shouldRetry(error)
        : isRetryableError(error);

      const attemptRecord: RetryAttempt = {
        attempt,
        success: false,
        error,
        duration: attemptResult?.duration ?? 0,
      };

      // If this is the last attempt or error is not retryable, don't delay
      if (attempt === finalConfig.maxAttempts || !shouldRetry) {
        attempts.push(attemptRecord);
        break;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = calculateDelay(attempt, finalConfig.baseDelay, finalConfig.maxDelay);
      attemptRecord.delay = delay;
      attempts.push(attemptRecord);

      // Log retry attempt
      await logError(
        `RETRY_ATTEMPT_${finalConfig.operationName.toUpperCase()}`,
        error,
        {
          attempt,
          maxAttempts: finalConfig.maxAttempts,
          delay,
          operationName: finalConfig.operationName,
        },
        "RetryMechanism"
      );

      // Check if we have enough time left for delay + next attempt
      const elapsed = Date.now() - startTime;
      if (elapsed + delay >= finalConfig.timeoutMs) {
        const timeoutError = new Error(`Not enough time for retry delay: ${elapsed + delay}ms >= ${finalConfig.timeoutMs}ms`);
        lastError = timeoutError;
        break;
      }

      // Wait before next attempt
      await sleep(delay);
    }
  }

  // All attempts failed
  const totalDuration = endTimer(timer, false)?.duration ?? 0;
  
  // Log final failure
  await logError(
    `RETRY_FAILED_${finalConfig.operationName.toUpperCase()}`,
    lastError,
    {
      totalAttempts: attempts.length,
      totalDuration,
      operationName: finalConfig.operationName,
      attempts: attempts.map(a => ({
        attempt: a.attempt,
        success: a.success,
        duration: a.duration,
        delay: a.delay,
      })),
    },
    "RetryMechanism"
  );

  return {
    success: false,
    error: lastError,
    attempts,
    totalDuration,
    finalAttempt: attempts.length,
  };
}

/**
 * Calculate delay for exponential backoff with jitter
 * 
 * @param attempt - Current attempt number (1-based)
 * @param baseDelay - Base delay in milliseconds
 * @param maxDelay - Maximum delay in milliseconds
 * @returns Delay in milliseconds
 */
function calculateDelay(attempt: number, baseDelay: number, maxDelay: number): number {
  // Exponential backoff: baseDelay * 2^(attempt-1)
  const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
  
  // Add jitter (±25% randomization)
  const jitter = exponentialDelay * 0.25 * (Math.random() - 0.5);
  const delayWithJitter = exponentialDelay + jitter;
  
  // Cap at maximum delay
  return Math.min(delayWithJitter, maxDelay);
}

/**
 * Determine if an error should trigger a retry
 * 
 * @param error - Error to evaluate
 * @returns True if error is retryable
 */
function isRetryableError(error: unknown): boolean {
  if (!error) return false;

  // Handle Error objects
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // Network-related errors are retryable
    if (message.includes('network') || 
        message.includes('timeout') || 
        message.includes('connection') ||
        message.includes('fetch')) {
      return true;
    }
    
    // Validation errors are not retryable
    if (message.includes('validation') || 
        message.includes('invalid') ||
        message.includes('malformed')) {
      return false;
    }
  }

  // Handle HTTP response errors
  if (typeof error === 'object' && error !== null && 'status' in error) {
    const status = (error as { status: number }).status;
    
    // 4xx client errors are generally not retryable
    if (status >= 400 && status < 500) {
      // Except for rate limiting and request timeout
      return status === 408 || status === 429;
    }
    
    // 5xx server errors are retryable
    if (status >= 500) {
      return true;
    }
  }

  // Default: retry unknown errors
  return true;
}

/**
 * Sleep for specified milliseconds
 * 
 * @param ms - Milliseconds to sleep
 * @returns Promise that resolves after delay
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export type { RetryConfig, RetryResult, RetryAttempt };
